<!DOCTYPE html>
<html lang="en" data-bs-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}<PERSON>o OAuth <PERSON>gin{% endblock %}</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    {% block extra_css %}{% endblock %}
</head>
<body>
    {% if 'username' in session %}
    <!-- Top Header for all devices -->
    <header class="app-header">
        <div class="container-fluid d-flex justify-content-between align-items-center">
            <div class="app-title">
                <a href="{{ url_for('welcome') }}">
                    <i class="fas fa-key me-2"></i>Maximo OAuth
                </a>
            </div>
            <div class="d-flex align-items-center">
                <!-- Desktop Navigation -->
                <div class="d-none d-lg-flex me-3">
                    <a href="{{ url_for('welcome') }}" class="nav-link me-3" data-nav="home">
                        <i class="fas fa-home me-1"></i>Home
                    </a>
                    <a href="{{ url_for('profile') }}" class="nav-link me-3" data-nav="profile">
                        <i class="fas fa-user me-1"></i>Profile
                    </a>
                    <a href="{{ url_for('enhanced_profile') }}" class="nav-link me-3" data-nav="enhanced-profile">
                        <i class="fas fa-rocket me-1"></i>Enhanced Profile
                    </a>
                    <a href="{{ url_for('sync') }}" class="nav-link me-3" data-nav="sync">
                        <i class="fas fa-sync-alt me-1"></i>Sync
                    </a>
                    <a href="{{ url_for('enhanced_workorders') }}" class="nav-link me-3" data-nav="workorders">
                        <i class="fas fa-clipboard-list me-1"></i>Work Orders
                    </a>
                </div>

                <!-- Hamburger Menu for Tablet -->
                <div class="d-lg-none d-md-flex me-3">
                    <button class="btn btn-outline-light hamburger-btn" type="button" data-bs-toggle="offcanvas" data-bs-target="#navigationOffcanvas" aria-controls="navigationOffcanvas">
                        <i class="fas fa-bars"></i>
                    </button>
                </div>

                <!-- User Info -->
                <div class="user-info-container d-none d-md-flex align-items-center">
                    <div class="dropdown">
                        <button class="btn btn-outline-light btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-user-circle me-1"></i>{{ session['username'] }}
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="{{ url_for('profile') }}"><i class="fas fa-user me-2"></i>Profile</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('enhanced_profile') }}"><i class="fas fa-rocket me-2"></i>Enhanced Profile</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('logout') }}"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Offcanvas Navigation for Tablet -->
    <div class="offcanvas offcanvas-end d-lg-none" tabindex="-1" id="navigationOffcanvas" aria-labelledby="navigationOffcanvasLabel">
        <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="navigationOffcanvasLabel">
                <i class="fas fa-user-circle me-2"></i>{{ session['username'] }}
            </h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
        </div>
        <div class="offcanvas-body">
            <nav class="nav flex-column">
                <a class="nav-link offcanvas-nav-link" href="{{ url_for('welcome') }}" data-nav="home">
                    <i class="fas fa-home me-2"></i>Home
                </a>
                <a class="nav-link offcanvas-nav-link" href="{{ url_for('profile') }}" data-nav="profile">
                    <i class="fas fa-user me-2"></i>Profile
                </a>
                <a class="nav-link offcanvas-nav-link" href="{{ url_for('enhanced_profile') }}" data-nav="enhanced-profile">
                    <i class="fas fa-rocket me-2"></i>Enhanced Profile
                </a>
                <a class="nav-link offcanvas-nav-link" href="{{ url_for('sync') }}" data-nav="sync">
                    <i class="fas fa-sync-alt me-2"></i>Sync
                </a>
                <a class="nav-link offcanvas-nav-link" href="{{ url_for('enhanced_workorders') }}" data-nav="workorders">
                    <i class="fas fa-clipboard-list me-2"></i>Work Orders
                </a>
                <hr>
                <a class="nav-link offcanvas-nav-link text-danger" href="{{ url_for('logout') }}">
                    <i class="fas fa-sign-out-alt me-2"></i>Logout
                </a>
            </nav>
        </div>
    </div>

    <!-- Enhanced Bottom Navigation for Mobile -->
    <nav class="mobile-nav d-md-none">
        <a href="{{ url_for('welcome') }}" class="nav-link" data-nav="home">
            <div class="nav-icon">
                <i class="fas fa-home"></i>
            </div>
            <span class="nav-label">Home</span>
        </a>
        <a href="{{ url_for('enhanced_workorders') }}" class="nav-link" data-nav="workorders">
            <div class="nav-icon">
                <i class="fas fa-clipboard-list"></i>
            </div>
            <span class="nav-label">Work Orders</span>
        </a>
        <a href="{{ url_for('enhanced_profile') }}" class="nav-link" data-nav="enhanced-profile">
            <div class="nav-icon">
                <i class="fas fa-rocket"></i>
            </div>
            <span class="nav-label">Profile</span>
        </a>
        <a href="{{ url_for('sync') }}" class="nav-link" data-nav="sync">
            <div class="nav-icon">
                <i class="fas fa-sync-alt"></i>
            </div>
            <span class="nav-label">Sync</span>
        </a>
        <button class="nav-link mobile-menu-btn" type="button" data-bs-toggle="offcanvas" data-bs-target="#mobileMenuOffcanvas" aria-controls="mobileMenuOffcanvas">
            <div class="nav-icon">
                <i class="fas fa-ellipsis-h"></i>
            </div>
            <span class="nav-label">More</span>
        </button>
    </nav>

    <!-- Mobile Menu Offcanvas -->
    <div class="offcanvas offcanvas-bottom mobile-menu-offcanvas" tabindex="-1" id="mobileMenuOffcanvas" aria-labelledby="mobileMenuOffcanvasLabel">
        <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="mobileMenuOffcanvasLabel">
                <i class="fas fa-user-circle me-2"></i>{{ session['username'] }}
            </h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
        </div>
        <div class="offcanvas-body">
            <div class="mobile-menu-grid">
                <a href="{{ url_for('profile') }}" class="mobile-menu-item">
                    <div class="mobile-menu-icon">
                        <i class="fas fa-user"></i>
                    </div>
                    <span>Basic Profile</span>
                </a>
                <a href="/api-docs/mxapiste" class="mobile-menu-item">
                    <div class="mobile-menu-icon">
                        <i class="fas fa-building"></i>
                    </div>
                    <span>Site API</span>
                </a>
                <button class="mobile-menu-item" id="mobileThemeToggle">
                    <div class="mobile-menu-icon">
                        <i class="fas fa-moon"></i>
                    </div>
                    <span>Dark Mode</span>
                </button>
                <a href="{{ url_for('logout') }}" class="mobile-menu-item logout-item">
                    <div class="mobile-menu-icon">
                        <i class="fas fa-sign-out-alt"></i>
                    </div>
                    <span>Logout</span>
                </a>
            </div>
        </div>
    </div>
    {% endif %}

    <main class="app-content">
        <div class="container">
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ category if category != 'error' else 'danger' }} alert-dismissible fade show">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            {% block content %}{% endblock %}
        </div>
    </main>

    <footer class="app-footer">
        <div class="container text-center">
            <p class="mb-0">Developed by Praba Krishna @2023</p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    {% block extra_js %}{% endblock %}
</body>
</html>
