// Main JavaScript for Maximo OAuth Login

document.addEventListener('DOMContentLoaded', function() {
    // Auto-dismiss alerts after 5 seconds
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
        setTimeout(() => {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        }, 5000);
    });
    
    // Handle dark mode toggle
    const themeSwitch = document.getElementById('themeSwitch');
    if (themeSwitch) {
        // Check for saved theme preference or respect OS preference
        const savedTheme = localStorage.getItem('theme');
        const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
        
        // Set initial state
        if (savedTheme === 'dark' || (!savedTheme && prefersDark)) {
            document.documentElement.setAttribute('data-bs-theme', 'dark');
            themeSwitch.checked = true;
        }
        
        // Handle toggle changes
        themeSwitch.addEventListener('change', function() {
            if (this.checked) {
                document.documentElement.setAttribute('data-bs-theme', 'dark');
                localStorage.setItem('theme', 'dark');
            } else {
                document.documentElement.setAttribute('data-bs-theme', 'light');
                localStorage.setItem('theme', 'light');
            }
        });
    }
    
    // Enhanced navigation active states
    const currentPath = window.location.pathname;

    // Handle mobile navigation active states
    const mobileNavLinks = document.querySelectorAll('.mobile-nav .nav-link[data-nav]');
    const desktopNavLinks = document.querySelectorAll('.app-header .nav-link[data-nav]');
    const offcanvasNavLinks = document.querySelectorAll('.offcanvas-nav-link[data-nav]');

    // Determine current page
    let currentNav = 'home';
    if (currentPath.includes('/profile')) {
        currentNav = currentPath.includes('/enhanced') ? 'enhanced-profile' : 'profile';
    } else if (currentPath.includes('/workorder')) {
        currentNav = 'workorders';
    } else if (currentPath.includes('/sync')) {
        currentNav = 'sync';
    }

    // Set active states
    [...mobileNavLinks, ...desktopNavLinks, ...offcanvasNavLinks].forEach(link => {
        const navType = link.getAttribute('data-nav');
        if (navType === currentNav) {
            link.classList.add('active');
        } else {
            link.classList.remove('active');
        }
    });

    // Handle mobile theme toggle
    const mobileThemeToggle = document.getElementById('mobileThemeToggle');
    if (mobileThemeToggle) {
        const updateMobileThemeIcon = () => {
            const isDark = document.documentElement.getAttribute('data-bs-theme') === 'dark';
            const icon = mobileThemeToggle.querySelector('i');
            const text = mobileThemeToggle.querySelector('span');

            if (isDark) {
                icon.className = 'fas fa-sun';
                text.textContent = 'Light Mode';
            } else {
                icon.className = 'fas fa-moon';
                text.textContent = 'Dark Mode';
            }
        };

        updateMobileThemeIcon();

        mobileThemeToggle.addEventListener('click', function() {
            const isDark = document.documentElement.getAttribute('data-bs-theme') === 'dark';

            if (isDark) {
                document.documentElement.setAttribute('data-bs-theme', 'light');
                localStorage.setItem('theme', 'light');
            } else {
                document.documentElement.setAttribute('data-bs-theme', 'dark');
                localStorage.setItem('theme', 'dark');
            }

            updateMobileThemeIcon();

            // Close the offcanvas
            const offcanvas = bootstrap.Offcanvas.getInstance(document.getElementById('mobileMenuOffcanvas'));
            if (offcanvas) {
                offcanvas.hide();
            }
        });
    }

    // Handle navigation link clicks with haptic feedback (if supported)
    const allNavLinks = document.querySelectorAll('.nav-link, .mobile-menu-item');
    allNavLinks.forEach(link => {
        link.addEventListener('click', function() {
            // Haptic feedback for mobile devices
            if ('vibrate' in navigator) {
                navigator.vibrate(50);
            }
        });
    });

    // Auto-close offcanvas when clicking navigation links
    const offcanvasLinks = document.querySelectorAll('.offcanvas-nav-link');
    offcanvasLinks.forEach(link => {
        link.addEventListener('click', function() {
            const offcanvasElement = this.closest('.offcanvas');
            if (offcanvasElement) {
                const offcanvas = bootstrap.Offcanvas.getInstance(offcanvasElement);
                if (offcanvas) {
                    offcanvas.hide();
                }
            }
        });
    });
});
