// Enhanced Work Orders JavaScript with Card View Support

class EnhancedWorkOrdersManager {
    constructor() {
        this.currentView = 'card'; // Default to card view
        this.workorders = [];
        this.selectedWorkorders = new Set();
        this.init();
    }

    init() {
        this.bindEventListeners();
        this.setInitialView();
        this.renderWorkorders(); // Initial render
    }

    bindEventListeners() {
        // View toggle button
        const toggleViewBtn = document.getElementById('toggleViewBtn');
        if (toggleViewBtn) {
            toggleViewBtn.addEventListener('click', () => this.toggleView());
        }

        // Select all functionality
        const selectAllBtn = document.getElementById('selectAllBtn');
        if (selectAllBtn) {
            selectAllBtn.addEventListener('click', () => this.selectAll());
        }

        // Clear selection functionality
        const clearSelectionBtn = document.getElementById('clearSelectionBtn');
        if (clearSelectionBtn) {
            clearSelectionBtn.addEventListener('click', () => this.clearSelection());
        }
    }

    setInitialView() {
        // Default to card view on mobile, table view on desktop
        const isMobile = window.innerWidth < 992;
        this.currentView = isMobile ? 'card' : 'card'; // Default to card view for now
        this.updateViewDisplay();
    }

    toggleView() {
        this.currentView = this.currentView === 'card' ? 'table' : 'card';
        this.updateViewDisplay();
        this.renderWorkorders();
    }

    updateViewDisplay() {
        const cardView = document.getElementById('workordersCardView');
        const tableView = document.getElementById('workordersTableView');
        const toggleBtn = document.getElementById('toggleViewBtn');

        console.log('Updating view display to:', this.currentView);
        console.log('Card view element:', cardView);
        console.log('Table view element:', tableView);

        if (this.currentView === 'card') {
            cardView?.classList.remove('d-none');
            tableView?.classList.add('d-none');
            if (toggleBtn) {
                toggleBtn.innerHTML = '<i class="fas fa-table me-1"></i><span class="d-none d-md-inline">Table View</span>';
            }
        } else {
            cardView?.classList.add('d-none');
            tableView?.classList.remove('d-none');
            if (toggleBtn) {
                toggleBtn.innerHTML = '<i class="fas fa-th-large me-1"></i><span class="d-none d-md-inline">Card View</span>';
            }
        }
    }

    renderWorkorders() {
        if (this.currentView === 'card') {
            this.renderCardView();
        } else {
            this.renderTableView();
        }
    }

    renderCardView() {
        const container = document.getElementById('workordersCardContainer');
        if (!container) {
            console.warn('Card container not found');
            return;
        }

        console.log('Rendering card view with', this.workorders.length, 'work orders');

        if (this.workorders.length === 0) {
            container.innerHTML = `
                <div class="text-center py-5">
                    <i class="fas fa-search fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">Ready to search work orders</h5>
                    <p class="text-muted">Enter search criteria above and click "Search" to find work orders</p>
                </div>
            `;
            return;
        }

        container.innerHTML = this.workorders.map(wo => this.createWorkOrderCard(wo)).join('');
        this.bindCardEventListeners();
    }

    createWorkOrderCard(workorder) {
        const isSelected = this.selectedWorkorders.has(workorder.wonum);
        const priorityClass = `priority-${workorder.wopriority || 3}`;
        const statusColor = this.getStatusColor(workorder.status);

        return `
            <div class="workorder-card ${isSelected ? 'selected' : ''}" data-wonum="${workorder.wonum}">
                <div class="workorder-priority-indicator ${priorityClass}"></div>
                
                <div class="workorder-card-header">
                    <div class="workorder-card-title">
                        <div class="d-flex align-items-center">
                            <input type="checkbox" class="form-check-input me-2 workorder-checkbox" 
                                   data-wonum="${workorder.wonum}" ${isSelected ? 'checked' : ''}>
                            <span>${workorder.wonum}</span>
                        </div>
                        <span class="badge ${statusColor}">${workorder.status || 'N/A'}</span>
                    </div>
                    <div class="workorder-card-subtitle">
                        <i class="fas fa-map-marker-alt me-1"></i>${workorder.siteid || 'N/A'}
                    </div>
                </div>

                <div class="workorder-card-body">
                    <div class="mb-3">
                        <h6 class="mb-2">${workorder.description || 'No description available'}</h6>
                    </div>

                    <div class="workorder-details-grid">
                        <div class="workorder-detail-item">
                            <span class="workorder-detail-label">Priority</span>
                            <span class="workorder-detail-value ${this.getPriorityClass(workorder.wopriority)}">
                                ${this.getPriorityText(workorder.wopriority)}
                            </span>
                        </div>
                        <div class="workorder-detail-item">
                            <span class="workorder-detail-label">Work Type</span>
                            <span class="workorder-detail-value">${workorder.worktype || 'N/A'}</span>
                        </div>
                        <div class="workorder-detail-item">
                            <span class="workorder-detail-label">Assigned To</span>
                            <span class="workorder-detail-value">${workorder.lead || 'Unassigned'}</span>
                        </div>
                        <div class="workorder-detail-item">
                            <span class="workorder-detail-label">Location</span>
                            <span class="workorder-detail-value">${workorder.location || 'N/A'}</span>
                        </div>
                        <div class="workorder-detail-item">
                            <span class="workorder-detail-label">Asset</span>
                            <span class="workorder-detail-value">${workorder.assetnum || 'N/A'}</span>
                        </div>
                        <div class="workorder-detail-item">
                            <span class="workorder-detail-label">Report Date</span>
                            <span class="workorder-detail-value">${this.formatDate(workorder.reportdate)}</span>
                        </div>
                    </div>
                </div>

                <div class="workorder-card-actions">
                    <button class="btn btn-primary btn-sm" onclick="viewWorkOrderDetails('${workorder.wonum}')">
                        <i class="fas fa-eye me-1"></i>View Details
                    </button>
                    <button class="btn btn-success btn-sm materials-check-btn" 
                            onclick="checkMaterials('${workorder.wonum}', '${workorder.siteid}')">
                        <i class="fas fa-boxes me-1"></i>Materials
                    </button>
                    <button class="btn btn-info btn-sm" onclick="openLaborSearch('${workorder.wonum}')">
                        <i class="fas fa-users me-1"></i>Labor
                    </button>
                </div>
            </div>
        `;
    }

    bindCardEventListeners() {
        // Handle checkbox changes
        const checkboxes = document.querySelectorAll('.workorder-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.addEventListener('change', (e) => {
                const wonum = e.target.dataset.wonum;
                if (e.target.checked) {
                    this.selectedWorkorders.add(wonum);
                } else {
                    this.selectedWorkorders.delete(wonum);
                }
                this.updateSelectionUI();
            });
        });

        // Handle card clicks (excluding interactive elements)
        const cards = document.querySelectorAll('.workorder-card');
        cards.forEach(card => {
            card.addEventListener('click', (e) => {
                if (!e.target.closest('input, button, .btn')) {
                    const wonum = card.dataset.wonum;
                    viewWorkOrderDetails(wonum);
                }
            });
        });
    }

    renderTableView() {
        // Use the existing table population function
        if (typeof populateWorkOrdersTable === 'function') {
            // Don't call populateWorkOrdersTable here to avoid infinite loop
            // The table is already populated by the search function
        }
    }

    selectAll() {
        this.workorders.forEach(wo => this.selectedWorkorders.add(wo.wonum));
        this.updateSelectionUI();
        this.renderWorkorders();
    }

    clearSelection() {
        this.selectedWorkorders.clear();
        this.updateSelectionUI();
        this.renderWorkorders();
    }

    updateSelectionUI() {
        const selectAllBtn = document.getElementById('selectAllBtn');
        const clearSelectionBtn = document.getElementById('clearSelectionBtn');
        const count = this.selectedWorkorders.size;

        if (selectAllBtn) {
            selectAllBtn.innerHTML = `<i class="fas fa-check-square me-1"></i><span class="d-none d-md-inline">Select All${count > 0 ? ` (${count})` : ''}</span>`;
        }

        if (clearSelectionBtn) {
            clearSelectionBtn.disabled = count === 0;
        }
    }

    // Utility methods
    getStatusColor(status) {
        const statusColors = {
            'APPR': 'bg-success',
            'ASSIGN': 'bg-info',
            'READY': 'bg-primary',
            'INPRG': 'bg-warning',
            'DEFER': 'bg-secondary',
            'WAPPR': 'bg-warning',
            'WMATL': 'bg-danger'
        };
        return statusColors[status] || 'bg-secondary';
    }

    getPriorityClass(priority) {
        if (priority <= 2) return 'danger';
        if (priority === 3) return 'warning';
        return 'success';
    }

    getPriorityText(priority) {
        const priorities = {
            1: '1 - Critical',
            2: '2 - High',
            3: '3 - Medium',
            4: '4 - Low',
            5: '5 - Lowest'
        };
        return priorities[priority] || `${priority} - Unknown`;
    }

    formatDate(dateString) {
        if (!dateString) return 'N/A';
        try {
            return new Date(dateString).toLocaleDateString();
        } catch {
            return dateString;
        }
    }

    // Public method to update workorders data
    updateWorkorders(workorders) {
        console.log('Updating workorders with:', workorders?.length || 0, 'items');
        this.workorders = workorders || [];
        this.renderWorkorders();
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.workOrdersManager = new EnhancedWorkOrdersManager();
});

// Handle window resize
window.addEventListener('resize', function() {
    if (window.workOrdersManager) {
        const isMobile = window.innerWidth < 992;
        if (isMobile && window.workOrdersManager.currentView === 'table') {
            window.workOrdersManager.currentView = 'card';
            window.workOrdersManager.updateViewDisplay();
            window.workOrdersManager.renderWorkorders();
        }
    }
});
