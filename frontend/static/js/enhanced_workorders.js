// Enhanced Work Orders JavaScript with Card View Support

class EnhancedWorkOrdersManager {
    constructor() {
        this.currentView = 'card'; // Default to card view
        this.workorders = [];
        this.selectedWorkorders = new Set();
        this.init();
    }

    init() {
        this.bindEventListeners();
        this.setInitialView();
        this.renderWorkorders(); // Initial render
    }

    bindEventListeners() {
        console.log('Binding event listeners');

        // View toggle button
        const toggleViewBtn = document.getElementById('toggleViewBtn');
        if (toggleViewBtn) {
            console.log('Toggle view button found, adding listener');
            toggleViewBtn.addEventListener('click', () => {
                console.log('Toggle view button clicked');
                this.toggleView();
            });
        } else {
            console.warn('Toggle view button not found');
        }

        // Select all functionality
        const selectAllBtn = document.getElementById('selectAllBtn');
        if (selectAllBtn) {
            console.log('Select all button found, adding listener');
            selectAllBtn.addEventListener('click', () => {
                console.log('Select all button clicked');
                this.selectAll();
            });
        } else {
            console.warn('Select all button not found');
        }

        // Clear selection functionality
        const clearSelectionBtn = document.getElementById('clearSelectionBtn');
        if (clearSelectionBtn) {
            console.log('Clear selection button found, adding listener');
            clearSelectionBtn.addEventListener('click', () => {
                console.log('Clear selection button clicked');
                this.clearSelection();
            });
        } else {
            console.warn('Clear selection button not found');
        }
    }

    setInitialView() {
        // Default to card view on mobile, table view on desktop
        const isMobile = window.innerWidth < 992;
        this.currentView = isMobile ? 'card' : 'card'; // Default to card view for now
        this.updateViewDisplay();
    }

    toggleView() {
        this.currentView = this.currentView === 'card' ? 'table' : 'card';
        this.updateViewDisplay();
        this.renderWorkorders();
    }

    updateViewDisplay() {
        const cardView = document.getElementById('workordersCardView');
        const tableView = document.getElementById('workordersTableView');
        const toggleBtn = document.getElementById('toggleViewBtn');

        console.log('Updating view display to:', this.currentView);
        console.log('Card view element:', cardView);
        console.log('Table view element:', tableView);

        if (this.currentView === 'card') {
            cardView?.classList.remove('d-none');
            tableView?.classList.add('d-none');
            if (toggleBtn) {
                toggleBtn.innerHTML = '<i class="fas fa-table me-1"></i><span class="d-none d-md-inline">Table View</span>';
            }
        } else {
            cardView?.classList.add('d-none');
            tableView?.classList.remove('d-none');
            if (toggleBtn) {
                toggleBtn.innerHTML = '<i class="fas fa-th-large me-1"></i><span class="d-none d-md-inline">Card View</span>';
            }
        }
    }

    renderWorkorders() {
        if (this.currentView === 'card') {
            this.renderCardView();
        } else {
            this.renderTableView();
        }
    }

    renderCardView() {
        const container = document.getElementById('workordersCardContainer');
        if (!container) {
            console.warn('Card container not found');
            return;
        }

        console.log('Rendering card view with', this.workorders.length, 'work orders');

        if (this.workorders.length === 0) {
            container.innerHTML = `
                <div class="text-center py-5">
                    <i class="fas fa-search fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">Ready to search work orders</h5>
                    <p class="text-muted">Enter search criteria above and click "Search" to find work orders</p>
                </div>
            `;
            return;
        }

        container.innerHTML = this.workorders.map(wo => this.createWorkOrderCard(wo)).join('');
        this.bindCardEventListeners();
    }

    createWorkOrderCard(workorder) {
        const isSelected = this.selectedWorkorders.has(workorder.wonum);
        const priorityClass = `priority-${workorder.priority || workorder.wopriority || 3}`;
        const statusBadge = this.getStatusBadge(workorder.status);
        const priorityBadge = this.getPriorityBadge(workorder.priority || workorder.wopriority);

        return `
            <div class="workorder-card ${isSelected ? 'selected' : ''}" data-wonum="${workorder.wonum}">
                <div class="workorder-priority-indicator ${priorityClass}"></div>

                <div class="workorder-card-header">
                    <div class="workorder-card-title">
                        <div class="d-flex align-items-center">
                            <input type="checkbox" class="form-check-input me-2 work-order-checkbox"
                                   value="${workorder.wonum}" ${isSelected ? 'checked' : ''}
                                   onchange="updateSelectedCount()">
                            <a href="/enhanced-workorder-details/${workorder.wonum}" class="text-decoration-none">
                                <strong class="text-primary">${workorder.wonum}</strong>
                            </a>
                        </div>
                        <div class="d-flex gap-2">
                            ${statusBadge}
                            ${priorityBadge}
                        </div>
                    </div>
                    <div class="workorder-card-subtitle">
                        <i class="fas fa-map-marker-alt me-1"></i>
                        <span class="badge bg-secondary">${workorder.siteid || '-'}</span>
                    </div>
                </div>

                <div class="workorder-card-body">
                    <div class="mb-3">
                        <h6 class="mb-2" title="${workorder.description || ''}">${workorder.description || 'No description available'}</h6>
                    </div>

                    <div class="workorder-details-grid">
                        <div class="workorder-detail-item">
                            <span class="workorder-detail-label">Work Type</span>
                            <span class="workorder-detail-value">${workorder.worktype || '-'}</span>
                        </div>
                        <div class="workorder-detail-item">
                            <span class="workorder-detail-label">Assigned To</span>
                            <span class="workorder-detail-value">${workorder.assignedto || workorder.lead || '-'}</span>
                        </div>
                        <div class="workorder-detail-item">
                            <span class="workorder-detail-label">Location</span>
                            <span class="workorder-detail-value">${workorder.location || '-'}</span>
                        </div>
                        <div class="workorder-detail-item">
                            <span class="workorder-detail-label">Asset</span>
                            <span class="workorder-detail-value">${workorder.assetnum || '-'}</span>
                        </div>
                        <div class="workorder-detail-item">
                            <span class="workorder-detail-label">Report Date</span>
                            <span class="workorder-detail-value">${this.formatDate(workorder.reportdate)}</span>
                        </div>
                        <div class="workorder-detail-item">
                            <span class="workorder-detail-label">Materials</span>
                            <div id="materials-${workorder.wonum}" class="materials-check-container">
                                <button class="btn btn-sm btn-outline-secondary materials-check-btn"
                                        onclick="checkMaterials('${workorder.wonum}', '${workorder.siteid}')"
                                        title="Check for planned materials">
                                    <i class="fas fa-boxes"></i>
                                    <span class="d-none d-md-inline ms-1">Check Materials</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="workorder-card-actions">
                    <div class="row g-2">
                        <div class="col-6">
                            <button class="btn btn-success btn-sm w-100" onclick="executeIndividualMethod('approve', '${workorder.wonum}')" title="Approve">
                                <i class="fas fa-check me-1"></i>Approve
                            </button>
                        </div>
                        <div class="col-6">
                            <button class="btn btn-primary btn-sm w-100" onclick="executeIndividualMethod('start', '${workorder.wonum}')" title="Start">
                                <i class="fas fa-play me-1"></i>Start
                            </button>
                        </div>
                        <div class="col-6">
                            <button class="btn btn-warning btn-sm w-100" onclick="executeIndividualMethod('complete', '${workorder.wonum}')" title="Complete">
                                <i class="fas fa-flag-checkered me-1"></i>Complete
                            </button>
                        </div>
                        <div class="col-6">
                            <a href="/enhanced-workorder-details/${workorder.wonum}" class="btn btn-outline-info btn-sm w-100" title="View Details">
                                <i class="fas fa-eye me-1"></i>Details
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    bindCardEventListeners() {
        // Handle card clicks (excluding interactive elements)
        const cards = document.querySelectorAll('.workorder-card');
        cards.forEach(card => {
            card.addEventListener('click', (e) => {
                if (!e.target.closest('input, button, .btn, a')) {
                    const wonum = card.dataset.wonum;
                    window.location.href = `/enhanced-workorder-details/${wonum}`;
                }
            });
        });
    }

    renderTableView() {
        // Use the existing table population function
        if (typeof populateWorkOrdersTable === 'function') {
            // Don't call populateWorkOrdersTable here to avoid infinite loop
            // The table is already populated by the search function
        }
    }

    selectAll() {
        // Select all checkboxes in both views
        const checkboxes = document.querySelectorAll('.work-order-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = true;
        });

        // Update the global selection count
        if (typeof updateSelectedCount === 'function') {
            updateSelectedCount();
        }
    }

    clearSelection() {
        // Clear all checkboxes in both views
        const checkboxes = document.querySelectorAll('.work-order-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = false;
        });

        // Update the global selection count
        if (typeof updateSelectedCount === 'function') {
            updateSelectedCount();
        }
    }

    updateSelectionUI() {
        // This will be handled by the existing updateSelectedCount function
        if (typeof updateSelectedCount === 'function') {
            updateSelectedCount();
        }
    }

    // Utility methods that match the table view functions
    getStatusBadge(status) {
        if (!status) return '<span class="text-muted">-</span>';

        const statusClasses = {
            'APPR': 'bg-success', 'READY': 'bg-success',
            'ASSIGN': 'bg-primary', 'INPRG': 'bg-primary',
            'WAPPR': 'bg-warning', 'WGOVT': 'bg-warning', 'WMATL': 'bg-warning', 'WSERV': 'bg-warning', 'WSCH': 'bg-warning',
            'PACK': 'bg-secondary', 'DEFER': 'bg-secondary'
        };

        const badgeClass = statusClasses[status] || 'bg-info';
        return `<span class="badge ${badgeClass}">${status}</span>`;
    }

    getPriorityBadge(priority) {
        if (!priority) return '<span class="text-muted">-</span>';

        const priorityNum = parseInt(priority);
        let badgeClass = 'bg-success';

        if (priorityNum <= 2) badgeClass = 'bg-danger';
        else if (priorityNum <= 3) badgeClass = 'bg-warning';

        return `<span class="badge ${badgeClass}">${priority}</span>`;
    }

    getPriorityClass(priority) {
        if (priority <= 2) return 'danger';
        if (priority === 3) return 'warning';
        return 'success';
    }

    getPriorityText(priority) {
        const priorities = {
            1: '1 - Critical',
            2: '2 - High',
            3: '3 - Medium',
            4: '4 - Low',
            5: '5 - Lowest'
        };
        return priorities[priority] || `${priority} - Unknown`;
    }

    formatDate(dateString) {
        if (!dateString) return 'N/A';
        try {
            return new Date(dateString).toLocaleDateString();
        } catch {
            return dateString;
        }
    }

    // Public method to update workorders data
    updateWorkorders(workorders) {
        console.log('Updating workorders with:', workorders?.length || 0, 'items');
        this.workorders = workorders || [];
        this.renderWorkorders();
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('Initializing Enhanced Work Orders Manager');
    try {
        window.workOrdersManager = new EnhancedWorkOrdersManager();
        console.log('Work Orders Manager initialized successfully');
    } catch (error) {
        console.error('Error initializing Work Orders Manager:', error);
    }
});

// Handle window resize
window.addEventListener('resize', function() {
    if (window.workOrdersManager) {
        const isMobile = window.innerWidth < 992;
        if (isMobile && window.workOrdersManager.currentView === 'table') {
            window.workOrdersManager.currentView = 'card';
            window.workOrdersManager.updateViewDisplay();
            window.workOrdersManager.renderWorkorders();
        }
    }
});

// Global test function for debugging
window.testCardView = function() {
    const sampleWorkorders = [
        {
            wonum: 'WO-2024-001',
            siteid: 'SITE01',
            description: 'Sample work order for testing card view functionality with all fields',
            status: 'APPR',
            priority: 2,
            worktype: 'PM',
            assignedto: 'John Doe',
            location: 'Building A - Room 101',
            assetnum: 'ASSET001',
            reportdate: '2024-01-15T10:30:00'
        },
        {
            wonum: 'WO-2024-002',
            siteid: 'SITE02',
            description: 'Another test work order with different priority and status',
            status: 'INPRG',
            priority: 1,
            worktype: 'CM',
            assignedto: 'Jane Smith',
            location: 'Building B - Basement',
            assetnum: 'ASSET002',
            reportdate: '2024-01-16T14:45:00'
        },
        {
            wonum: 'WO-2024-003',
            siteid: 'SITE03',
            description: 'Emergency repair work order with high priority',
            status: 'WMATL',
            priority: 1,
            worktype: 'EM',
            assignedto: 'Mike Johnson',
            location: 'Building C - Floor 3',
            assetnum: 'ASSET003',
            reportdate: '2024-01-17T08:15:00'
        }
    ];

    if (window.workOrdersManager) {
        console.log('Loading test data into card view...');
        window.workOrdersManager.updateWorkorders(sampleWorkorders);
        console.log('Test data loaded successfully');

        // Show the performance metrics for testing
        const performanceMetrics = document.getElementById('performanceMetrics');
        if (performanceMetrics) {
            performanceMetrics.style.display = 'block';
            document.getElementById('searchTime').textContent = '0.15s';
            document.getElementById('resultCount').textContent = sampleWorkorders.length;
            document.getElementById('currentPage').textContent = '1';
            document.getElementById('totalPages').textContent = '1';
        }

        // Update work order count
        const workorderCount = document.getElementById('workorderCount');
        if (workorderCount) {
            workorderCount.textContent = sampleWorkorders.length;
        }
    } else {
        console.error('Work orders manager not found');
    }
};
