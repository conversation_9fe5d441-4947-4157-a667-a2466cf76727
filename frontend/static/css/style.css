/* Enhanced Mobile-First Design System */

:root {
    /* Light theme colors */
    --primary-color: #2c3e50;
    --primary-color-rgb: 44, 62, 80;
    --secondary-color: #3498db;
    --secondary-color-rgb: 52, 152, 219;
    --accent-color: #e74c3c;
    --accent-color-rgb: 231, 76, 60;
    --success-color: #2ecc71;
    --success-color-rgb: 46, 204, 113;
    --warning-color: #f39c12;
    --warning-color-rgb: 243, 156, 18;
    --danger-color: #e74c3c;
    --danger-color-rgb: 231, 76, 60;
    --light-color: #ecf0f1;
    --light-color-rgb: 236, 240, 241;
    --dark-color: #34495e;
    --dark-color-rgb: 52, 73, 94;
    --background-color: #f5f7fa;
    --text-color: #1a1a1a;
    --text-secondary: #4a5568;
    --text-muted: #718096;
    --border-color: #dfe6e9;
    --card-bg: #ffffff;
    --header-bg: #2c3e50;
    --footer-bg: #2c3e50;
    --mobile-nav-bg: #2c3e50;
    --shadow-color: rgba(0, 0, 0, 0.1);
    --border-radius: 8px;
    --box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;

    /* Typography Scale */
    --font-size-xs: 0.75rem;    /* 12px */
    --font-size-sm: 0.875rem;   /* 14px */
    --font-size-base: 1rem;     /* 16px */
    --font-size-lg: 1.125rem;   /* 18px */
    --font-size-xl: 1.25rem;    /* 20px */
    --font-size-2xl: 1.5rem;    /* 24px */
    --font-size-3xl: 1.875rem;  /* 30px */
    --font-size-4xl: 2.25rem;   /* 36px */

    /* Line Heights */
    --line-height-tight: 1.25;
    --line-height-normal: 1.5;
    --line-height-relaxed: 1.75;

    /* Font Weights */
    --font-weight-light: 300;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --font-weight-extrabold: 800;

    /* Letter Spacing */
    --letter-spacing-tight: -0.025em;
    --letter-spacing-normal: 0;
    --letter-spacing-wide: 0.025em;
    --letter-spacing-wider: 0.05em;
    --letter-spacing-widest: 0.1em;
}

/* Dark theme colors */
[data-bs-theme="dark"] {
    --primary-color: #3498db;
    --primary-color-rgb: 52, 152, 219;
    --secondary-color: #2c3e50;
    --secondary-color-rgb: 44, 62, 80;
    --accent-color: #e74c3c;
    --accent-color-rgb: 231, 76, 60;
    --success-color: #2ecc71;
    --success-color-rgb: 46, 204, 113;
    --warning-color: #f39c12;
    --warning-color-rgb: 243, 156, 18;
    --danger-color: #e74c3c;
    --danger-color-rgb: 231, 76, 60;
    --light-color: #34495e;
    --light-color-rgb: 52, 73, 94;
    --dark-color: #ecf0f1;
    --dark-color-rgb: 236, 240, 241;
    --background-color: #1a1a2e;
    --text-color: #f7fafc;
    --text-secondary: #cbd5e0;
    --text-muted: #a0aec0;
    --border-color: #34495e;
    --card-bg: #16213e;
    --header-bg: #0f3460;
    --footer-bg: #0f3460;
    --mobile-nav-bg: #0f3460;
    --shadow-color: rgba(0, 0, 0, 0.3);
}

/* Enhanced Typography System */
body {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background-color: var(--background-color);
    color: var(--text-color);
    transition: var(--transition);
    padding-top: 60px; /* Space for fixed header */
    padding-bottom: 70px; /* Space for mobile nav */
    font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Helvetica Neue', Arial, sans-serif;
    font-size: var(--font-size-base);
    line-height: var(--line-height-normal);
    font-weight: var(--font-weight-normal);
    letter-spacing: var(--letter-spacing-normal);
    overflow-x: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

/* Heading Hierarchy */
h1, h2, h3, h4, h5, h6 {
    font-weight: var(--font-weight-bold);
    line-height: var(--line-height-tight);
    letter-spacing: var(--letter-spacing-tight);
    margin-top: 0;
    margin-bottom: 0.75rem;
    color: var(--text-color);
}

h1 {
    font-size: var(--font-size-4xl);
    font-weight: var(--font-weight-extrabold);
    line-height: 1.1;
}

h2 {
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-bold);
    line-height: 1.2;
}

h3 {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-bold);
    line-height: 1.25;
}

h4 {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
}

h5 {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
}

h6 {
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-semibold);
    text-transform: uppercase;
    letter-spacing: var(--letter-spacing-wide);
}

/* Paragraph and Text */
p {
    margin-top: 0;
    margin-bottom: 1rem;
    line-height: var(--line-height-relaxed);
}

.lead {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-light);
    line-height: var(--line-height-relaxed);
}

.text-small {
    font-size: var(--font-size-sm);
}

.text-xs {
    font-size: var(--font-size-xs);
}

.text-lg {
    font-size: var(--font-size-lg);
}

.text-xl {
    font-size: var(--font-size-xl);
}

/* Header styles */
.app-header {
    background-color: var(--header-bg);
    box-shadow: var(--box-shadow);
    height: 60px;
    display: flex;
    align-items: center;
    z-index: 1030;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
}

.app-title a {
    color: white;
    font-weight: bold;
    font-size: 1.3rem;
    letter-spacing: 0.5px;
    text-transform: uppercase;
    text-decoration: none;
}

.user-info {
    color: white;
    margin-right: 15px;
    opacity: 0.9;
}

/* Enhanced Mobile Navigation */
.mobile-nav {
    background-color: var(--mobile-nav-bg);
    box-shadow: 0 -2px 20px var(--shadow-color);
    height: 70px;
    z-index: 1020;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    display: flex;
    justify-content: space-around;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.mobile-nav .nav-link {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: rgba(255, 255, 255, 0.7);
    padding: 0.5rem 0.25rem;
    font-size: 0.7rem;
    transition: all 0.3s ease;
    flex: 1;
    text-decoration: none;
    border-radius: 12px;
    margin: 0.25rem;
    position: relative;
    min-height: 44px; /* Minimum touch target */
    border: none;
    background: none;
}

.mobile-nav .nav-link .nav-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 8px;
    transition: all 0.3s ease;
    margin-bottom: 0.25rem;
}

.mobile-nav .nav-link .nav-icon i {
    font-size: 1.2rem;
    transition: all 0.3s ease;
}

.mobile-nav .nav-link .nav-label {
    font-weight: 500;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
}

.mobile-nav .nav-link.active {
    color: white;
    transform: translateY(-2px);
}

.mobile-nav .nav-link.active .nav-icon {
    background-color: var(--primary-color);
    box-shadow: 0 4px 12px rgba(var(--primary-color-rgb), 0.4);
}

.mobile-nav .nav-link.active .nav-icon i {
    transform: scale(1.1);
}

.mobile-nav .nav-link:hover:not(.active) {
    color: white;
    transform: translateY(-1px);
}

.mobile-nav .nav-link:hover:not(.active) .nav-icon {
    background-color: rgba(255, 255, 255, 0.1);
}

.mobile-nav .nav-link:active {
    transform: translateY(0);
}

/* Mobile Menu Button Specific Styles */
.mobile-nav .mobile-menu-btn {
    cursor: pointer;
}

.mobile-nav .mobile-menu-btn:hover .nav-icon {
    background-color: rgba(255, 255, 255, 0.15);
}

/* Desktop navigation */
.app-header .nav-link {
    color: rgba(255, 255, 255, 0.8);
    transition: all 0.3s ease;
    text-decoration: none;
    padding: 0.5rem 0.75rem;
    border-radius: var(--border-radius);
    position: relative;
    min-height: 44px;
    display: flex;
    align-items: center;
}

.app-header .nav-link:hover {
    color: white;
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
}

.app-header .nav-link.active {
    color: white;
    background-color: rgba(255, 255, 255, 0.2);
}

/* Hamburger Button */
.hamburger-btn {
    min-height: 44px;
    min-width: 44px;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.hamburger-btn:hover {
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
}

/* User Info Dropdown */
.user-info-container .dropdown-toggle {
    min-height: 44px;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.user-info-container .dropdown-toggle:hover {
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
}

/* Offcanvas Navigation Styles */
.offcanvas-nav-link {
    color: var(--text-color);
    padding: 0.75rem 1rem;
    border-radius: var(--border-radius);
    transition: all 0.3s ease;
    text-decoration: none;
    min-height: 44px;
    display: flex;
    align-items: center;
    margin-bottom: 0.25rem;
}

.offcanvas-nav-link:hover {
    background-color: var(--primary-color);
    color: white;
    transform: translateX(5px);
}

.offcanvas-nav-link.active {
    background-color: var(--primary-color);
    color: white;
}

/* Mobile Menu Offcanvas */
.mobile-menu-offcanvas {
    border-radius: 20px 20px 0 0;
}

.mobile-menu-offcanvas .offcanvas-body {
    padding: 1.5rem;
}

.mobile-menu-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
}

.mobile-menu-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 1.5rem 1rem;
    background-color: var(--card-bg);
    border-radius: 16px;
    text-decoration: none;
    color: var(--text-color);
    transition: all 0.3s ease;
    min-height: 80px;
    border: 1px solid var(--border-color);
    cursor: pointer;
    border: none;
    font-family: inherit;
    font-size: inherit;
}

.mobile-menu-item:hover {
    background-color: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(var(--primary-color-rgb), 0.3);
}

.mobile-menu-item.logout-item:hover {
    background-color: var(--danger-color);
    box-shadow: 0 8px 25px rgba(var(--danger-color-rgb), 0.3);
}

.mobile-menu-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 12px;
    background-color: rgba(var(--primary-color-rgb), 0.1);
    margin-bottom: 0.5rem;
    transition: all 0.3s ease;
}

.mobile-menu-icon i {
    font-size: 1.2rem;
    color: var(--primary-color);
    transition: all 0.3s ease;
}

.mobile-menu-item:hover .mobile-menu-icon {
    background-color: rgba(255, 255, 255, 0.2);
}

.mobile-menu-item:hover .mobile-menu-icon i {
    color: white;
    transform: scale(1.1);
}

.mobile-menu-item span {
    font-size: 0.85rem;
    font-weight: 500;
    text-align: center;
}

/* Enhanced Main Content Area */
.app-content {
    flex: 1;
    padding: 1rem 0 5rem; /* Bottom padding for mobile nav */
    margin-top: 0.5rem;
    min-height: calc(100vh - 120px);
}

/* Mobile-First Container */
.container {
    padding-left: 1rem;
    padding-right: 1rem;
    max-width: 100%;
}

@media (min-width: 576px) {
    .container {
        padding-left: 1.5rem;
        padding-right: 1.5rem;
        max-width: 540px;
    }
}

@media (min-width: 768px) {
    .container {
        max-width: 720px;
        padding-left: 2rem;
        padding-right: 2rem;
    }

    .app-content {
        padding: 1.5rem 0 2rem;
    }
}

@media (min-width: 992px) {
    .container {
        max-width: 960px;
    }

    .app-content {
        padding: 2rem 0;
    }
}

@media (min-width: 1200px) {
    .container {
        max-width: 1140px;
    }
}

@media (min-width: 1400px) {
    .container {
        max-width: 1320px;
    }
}

/* Footer styles */
.app-footer {
    background-color: var(--footer-bg);
    padding: 1rem 0;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
    letter-spacing: 0.5px;
}

/* Modern Card System */
.card {
    background-color: var(--card-bg);
    border: 1px solid var(--border-color);
    margin-bottom: 1.5rem;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.card-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    border-bottom: none;
    padding: 1.25rem 1.5rem;
    color: white;
}

.card-body {
    padding: 1.5rem;
}

.card-footer {
    background-color: var(--light-color);
    border-top: 1px solid var(--border-color);
    padding: 1rem 1.5rem;
}

/* Work Order Cards */
.workorder-card {
    background-color: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 16px;
    margin-bottom: 1rem;
    overflow: hidden;
    transition: all 0.3s ease;
    position: relative;
}

.workorder-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
    border-color: var(--primary-color);
}

.workorder-card-header {
    padding: 1.25rem 1.5rem 0.75rem;
    border-bottom: 1px solid var(--border-color);
    background: linear-gradient(135deg, rgba(var(--primary-color-rgb), 0.05) 0%, rgba(var(--secondary-color-rgb), 0.05) 100%);
}

.workorder-card-title {
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.workorder-card-subtitle {
    font-size: 0.9rem;
    color: var(--text-secondary);
    margin-bottom: 0;
}

.workorder-card-body {
    padding: 1.25rem 1.5rem;
}

.workorder-details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1.25rem;
}

.workorder-detail-item {
    display: flex;
    flex-direction: column;
}

.workorder-detail-label {
    font-size: 0.75rem;
    color: var(--text-secondary);
    text-transform: uppercase;
    font-weight: 600;
    letter-spacing: 0.5px;
    margin-bottom: 0.25rem;
}

.workorder-detail-value {
    font-weight: 600;
    color: var(--text-color);
    font-size: 0.95rem;
}

.workorder-detail-value.highlight {
    color: var(--primary-color);
}

.workorder-detail-value.success {
    color: var(--success-color);
}

.workorder-detail-value.warning {
    color: var(--warning-color);
}

.workorder-detail-value.danger {
    color: var(--danger-color);
}

.workorder-card-actions {
    padding: 1rem 1.5rem;
    background-color: rgba(var(--light-color-rgb), 0.3);
    border-top: 1px solid var(--border-color);
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
}

.workorder-status-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    padding: 0.375rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.workorder-priority-indicator {
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    border-radius: 0 0 0 16px;
}

.priority-1 { background-color: var(--danger-color); }
.priority-2 { background-color: var(--warning-color); }
.priority-3 { background-color: var(--secondary-color); }
.priority-4 { background-color: var(--success-color); }
.priority-5 { background-color: var(--light-color); }

/* Labor Cards */
.labor-card {
    background-color: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    margin-bottom: 1rem;
    overflow: hidden;
    transition: all 0.3s ease;
    position: relative;
}

.labor-card:hover {
    transform: translateY(-1px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
    border-color: var(--primary-color);
}

.labor-card-header {
    padding: 1rem 1.25rem 0.75rem;
    background: linear-gradient(135deg, rgba(var(--success-color-rgb), 0.05) 0%, rgba(var(--primary-color-rgb), 0.05) 100%);
    border-bottom: 1px solid var(--border-color);
}

.labor-card-title {
    font-size: 1rem;
    font-weight: 700;
    color: var(--success-color);
    margin-bottom: 0.25rem;
}

.labor-card-subtitle {
    font-size: 0.85rem;
    color: var(--text-secondary);
    margin-bottom: 0;
}

.labor-card-body {
    padding: 1rem 1.25rem;
}

.labor-details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.labor-card-actions {
    padding: 0.75rem 1.25rem;
    background-color: rgba(var(--light-color-rgb), 0.3);
    border-top: 1px solid var(--border-color);
    display: flex;
    gap: 0.5rem;
    justify-content: flex-end;
}

/* Inventory Cards - Enhanced from existing */
.inventory-item-card {
    border: 1px solid var(--border-color);
    border-radius: 12px;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
    background: var(--card-bg);
    overflow: hidden;
}

.inventory-item-card:hover {
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
    border-color: var(--primary-color);
}

.inventory-item-header {
    background: linear-gradient(135deg, rgba(var(--secondary-color-rgb), 0.05) 0%, rgba(var(--primary-color-rgb), 0.05) 100%);
    padding: 1rem 1.25rem;
    border-bottom: 1px solid var(--border-color);
}

.inventory-item-body {
    padding: 1rem 1.25rem;
}

.inventory-item-title {
    font-weight: 700;
    color: var(--secondary-color);
    margin: 0;
    font-size: 1rem;
}

.inventory-item-subtitle {
    color: var(--text-secondary);
    font-size: 0.85rem;
    margin-top: 0.25rem;
}

.inventory-item-description {
    color: var(--text-color);
    margin: 0.75rem 0;
    font-size: 0.9rem;
    line-height: 1.4;
}

.inventory-details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

/* Card Responsive Design */
@media (max-width: 768px) {
    .card-body {
        padding: 1rem;
    }

    .card-header {
        padding: 1rem;
    }

    .workorder-card-header,
    .labor-card-header,
    .inventory-item-header {
        padding: 1rem;
    }

    .workorder-card-body,
    .labor-card-body,
    .inventory-item-body {
        padding: 1rem;
    }

    .workorder-details-grid,
    .labor-details-grid,
    .inventory-details-grid {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }

    .workorder-card-actions,
    .labor-card-actions {
        padding: 0.75rem 1rem;
        flex-direction: column;
    }

    .workorder-card-actions .btn,
    .labor-card-actions .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }

    .workorder-card-actions .btn:last-child,
    .labor-card-actions .btn:last-child {
        margin-bottom: 0;
    }
}

@media (max-width: 576px) {
    .card {
        margin-bottom: 1rem;
        border-radius: 12px;
    }

    .workorder-card,
    .labor-card,
    .inventory-item-card {
        border-radius: 12px;
        margin-bottom: 0.75rem;
    }

    .workorder-card-title,
    .labor-card-title,
    .inventory-item-title {
        font-size: 0.95rem;
    }

    .workorder-details-grid,
    .labor-details-grid,
    .inventory-details-grid {
        gap: 0.5rem;
    }
}

.card-header {
    background-color: rgba(var(--primary-color-rgb), 0.1);
    border-bottom: 1px solid var(--border-color);
    font-weight: 600;
    padding: 1rem 1.25rem;
}

.card-body {
    padding: 1.25rem;
}

.card-footer {
    background-color: rgba(var(--primary-color-rgb), 0.05);
    border-top: 1px solid var(--border-color);
    padding: 1rem 1.25rem;
}

/* Login page */
.login-container {
    max-width: 400px;
    margin: 0 auto;
}

/* Alert customization */
.alert {
    margin-bottom: 1.5rem;
    border-radius: var(--border-radius);
    border: none;
    box-shadow: var(--box-shadow);
    padding: 1rem 1.25rem;
}

/* Enhanced Form Controls */
.form-control, .form-select {
    background-color: var(--card-bg);
    color: var(--text-color);
    border: 2px solid var(--border-color);
    border-radius: 12px;
    padding: 0.875rem 1rem;
    font-size: 1rem;
    line-height: 1.5;
    transition: all 0.3s ease;
    min-height: 44px; /* Touch target */
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(var(--primary-color-rgb), 0.15);
    background-color: var(--card-bg);
    color: var(--text-color);
    outline: none;
}

.form-control:hover:not(:focus), .form-select:hover:not(:focus) {
    border-color: var(--primary-color);
}

.input-group-text {
    border-radius: 12px;
    padding: 0.875rem 1rem;
    background-color: var(--light-color);
    border: 2px solid var(--border-color);
    color: var(--text-color);
    min-height: 44px;
    display: flex;
    align-items: center;
}

.input-group .form-control:not(:first-child) {
    border-left: none;
    border-radius: 0 12px 12px 0;
}

.input-group .input-group-text:not(:last-child) {
    border-right: none;
    border-radius: 12px 0 0 12px;
}

/* Form Labels */
.form-label {
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 0.5rem;
    font-size: 0.95rem;
}

/* Form Text */
.form-text {
    font-size: 0.85rem;
    color: var(--text-color);
    opacity: 0.7;
    margin-top: 0.25rem;
}

/* Mobile Form Optimizations */
@media (max-width: 768px) {
    .form-control, .form-select {
        font-size: 16px; /* Prevents zoom on iOS */
        padding: 1rem;
    }

    .input-group-text {
        padding: 1rem;
    }

    .form-label {
        font-size: 1rem;
        margin-bottom: 0.75rem;
    }

    /* Stack form groups vertically on mobile */
    .row .col-md-2,
    .row .col-md-3,
    .row .col-md-4,
    .row .col-md-6 {
        margin-bottom: 1rem;
    }
}

/* Modern Button Design System */
.btn {
    border-radius: 12px;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    font-size: 0.95rem;
    letter-spacing: 0.5px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: none;
    position: relative;
    overflow: hidden;
    min-height: 44px; /* Minimum touch target */
    display: inline-flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    cursor: pointer;
    user-select: none;
    -webkit-tap-highlight-color: transparent;
}

.btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(var(--primary-color-rgb), 0.3);
}

.btn:active {
    transform: translateY(1px) scale(0.98);
}

/* Button Sizes */
.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.85rem;
    min-height: 36px;
    border-radius: 8px;
}

.btn-lg {
    padding: 1rem 2rem;
    font-size: 1.1rem;
    min-height: 52px;
    border-radius: 16px;
}

/* Primary Button */
.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(var(--primary-color-rgb), 0.3);
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--secondary-color) 0%, var(--primary-color) 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(var(--primary-color-rgb), 0.4);
    color: white;
}

.btn-primary:active {
    transform: translateY(0) scale(0.98);
    box-shadow: 0 2px 10px rgba(var(--primary-color-rgb), 0.3);
}

/* Secondary Button */
.btn-secondary {
    background-color: var(--light-color);
    color: var(--text-color);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.btn-secondary:hover {
    background-color: var(--border-color);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
    color: var(--text-color);
}

/* Outline Primary Button */
.btn-outline-primary {
    background-color: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
    box-shadow: none;
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(var(--primary-color-rgb), 0.3);
}

/* Outline Light Button (for dark backgrounds) */
.btn-outline-light {
    background-color: transparent;
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.8);
    box-shadow: none;
}

.btn-outline-light:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
    border-color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(255, 255, 255, 0.2);
}

/* Success Button */
.btn-success {
    background: linear-gradient(135deg, var(--success-color) 0%, #27ae60 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(var(--success-color-rgb), 0.3);
}

.btn-success:hover {
    background: linear-gradient(135deg, #27ae60 0%, var(--success-color) 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(var(--success-color-rgb), 0.4);
    color: white;
}

/* Warning Button */
.btn-warning {
    background: linear-gradient(135deg, var(--warning-color) 0%, #e67e22 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(var(--warning-color-rgb), 0.3);
}

.btn-warning:hover {
    background: linear-gradient(135deg, #e67e22 0%, var(--warning-color) 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(var(--warning-color-rgb), 0.4);
    color: white;
}

/* Danger Button */
.btn-danger {
    background: linear-gradient(135deg, var(--danger-color) 0%, #c0392b 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(var(--danger-color-rgb), 0.3);
}

.btn-danger:hover {
    background: linear-gradient(135deg, #c0392b 0%, var(--danger-color) 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(var(--danger-color-rgb), 0.4);
    color: white;
}

/* Info Button */
.btn-info {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.btn-info:hover {
    background: linear-gradient(135deg, #2980b9 0%, #3498db 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(52, 152, 219, 0.4);
    color: white;
}

/* Light Button */
.btn-light {
    background-color: var(--card-bg);
    color: var(--text-color);
    border: 1px solid var(--border-color);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.btn-light:hover {
    background-color: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
    color: var(--text-color);
}

/* Dark Button */
.btn-dark {
    background: linear-gradient(135deg, var(--dark-color) 0%, #2c3e50 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(var(--dark-color-rgb), 0.3);
}

.btn-dark:hover {
    background: linear-gradient(135deg, #2c3e50 0%, var(--dark-color) 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(var(--dark-color-rgb), 0.4);
    color: white;
}

/* Button Groups */
.btn-group .btn {
    border-radius: 0;
    margin: 0;
}

.btn-group .btn:first-child {
    border-radius: 12px 0 0 12px;
}

.btn-group .btn:last-child {
    border-radius: 0 12px 12px 0;
}

.btn-group .btn:only-child {
    border-radius: 12px;
}

/* Floating Action Button */
.btn-fab {
    width: 56px;
    height: 56px;
    border-radius: 50%;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    position: fixed;
    bottom: 90px;
    right: 20px;
    z-index: 1000;
    box-shadow: 0 6px 20px rgba(var(--primary-color-rgb), 0.4);
}

.btn-fab:hover {
    transform: scale(1.1);
    box-shadow: 0 8px 25px rgba(var(--primary-color-rgb), 0.5);
}

.btn-fab i {
    font-size: 1.5rem;
}

/* Loading Button State */
.btn-loading {
    pointer-events: none;
    opacity: 0.7;
}

.btn-loading::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    margin: auto;
    border: 2px solid transparent;
    border-top-color: currentColor;
    border-radius: 50%;
    animation: button-loading-spinner 1s ease infinite;
}

@keyframes button-loading-spinner {
    from {
        transform: rotate(0turn);
    }
    to {
        transform: rotate(1turn);
    }
}

/* Icon Buttons */
.btn-icon {
    width: 44px;
    height: 44px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 12px;
}

.btn-icon i {
    font-size: 1.1rem;
}

/* Button with Icon */
.btn i {
    margin-right: 0.5rem;
}

.btn i:only-child {
    margin-right: 0;
}

/* Responsive Button Adjustments */
@media (max-width: 768px) {
    .btn {
        padding: 0.75rem 1.25rem;
        font-size: 0.9rem;
        min-height: 44px;
    }

    .btn-sm {
        padding: 0.5rem 0.875rem;
        font-size: 0.8rem;
        min-height: 36px;
    }

    .btn-lg {
        padding: 1rem 1.75rem;
        font-size: 1rem;
        min-height: 52px;
    }

    .btn-group {
        flex-direction: column;
        width: 100%;
    }

    .btn-group .btn {
        border-radius: 12px;
        margin-bottom: 0.5rem;
    }

    .btn-group .btn:last-child {
        margin-bottom: 0;
    }
}

/* Badge styling */
.badge {
    padding: 0.5em 0.75em;
    font-weight: 600;
    border-radius: 20px;
    font-size: 0.8rem;
    letter-spacing: 0.5px;
}

/* Theme toggle */
.theme-toggle {
    padding: 1rem;
    border-top: 1px solid var(--border-color);
    margin-top: 1rem;
}

.form-check-input {
    cursor: pointer;
}

.form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.form-check-label {
    cursor: pointer;
}

/* Welcome page specific styles */
.welcome-header {
    margin-bottom: 2rem;
}

.welcome-header h2 {
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

/* Enhanced Text Colors with Better Contrast */
.text-primary {
    color: var(--primary-color) !important;
}

.text-secondary {
    color: var(--text-secondary) !important;
}

.text-muted {
    color: var(--text-muted) !important;
}

.text-success {
    color: var(--success-color) !important;
}

.text-warning {
    color: var(--warning-color) !important;
}

.text-danger {
    color: var(--danger-color) !important;
}

.text-info {
    color: var(--secondary-color) !important;
}

.bg-primary {
    background-color: var(--primary-color) !important;
}

/* Font Weights */
.fw-light { font-weight: var(--font-weight-light) !important; }
.fw-normal { font-weight: var(--font-weight-normal) !important; }
.fw-medium { font-weight: var(--font-weight-medium) !important; }
.fw-semibold { font-weight: var(--font-weight-semibold) !important; }
.fw-bold { font-weight: var(--font-weight-bold) !important; }
.fw-extrabold { font-weight: var(--font-weight-extrabold) !important; }

/* Letter Spacing */
.tracking-tight { letter-spacing: var(--letter-spacing-tight) !important; }
.tracking-normal { letter-spacing: var(--letter-spacing-normal) !important; }
.tracking-wide { letter-spacing: var(--letter-spacing-wide) !important; }
.tracking-wider { letter-spacing: var(--letter-spacing-wider) !important; }
.tracking-widest { letter-spacing: var(--letter-spacing-widest) !important; }

/* Line Heights */
.lh-tight { line-height: var(--line-height-tight) !important; }
.lh-normal { line-height: var(--line-height-normal) !important; }
.lh-relaxed { line-height: var(--line-height-relaxed) !important; }

/* Mobile Typography Adjustments */
@media (max-width: 768px) {
    h1 {
        font-size: var(--font-size-3xl);
    }

    h2 {
        font-size: var(--font-size-2xl);
    }

    h3 {
        font-size: var(--font-size-xl);
    }

    h4 {
        font-size: var(--font-size-lg);
    }

    .lead {
        font-size: var(--font-size-base);
    }

    body {
        font-size: var(--font-size-base);
        line-height: var(--line-height-relaxed);
    }
}

@media (max-width: 576px) {
    h1 {
        font-size: var(--font-size-2xl);
    }

    h2 {
        font-size: var(--font-size-xl);
    }

    h3 {
        font-size: var(--font-size-lg);
    }

    body {
        font-size: 0.9rem;
    }
}

/* Mobile-First Spacing System */
.mb-mobile-2 { margin-bottom: 0.5rem; }
.mb-mobile-3 { margin-bottom: 1rem; }
.mb-mobile-4 { margin-bottom: 1.5rem; }
.mb-mobile-5 { margin-bottom: 3rem; }

.mt-mobile-2 { margin-top: 0.5rem; }
.mt-mobile-3 { margin-top: 1rem; }
.mt-mobile-4 { margin-top: 1.5rem; }
.mt-mobile-5 { margin-top: 3rem; }

.p-mobile-2 { padding: 0.5rem; }
.p-mobile-3 { padding: 1rem; }
.p-mobile-4 { padding: 1.5rem; }

/* Mobile Content Spacing */
.mobile-section {
    margin-bottom: 1.5rem;
}

.mobile-section:last-child {
    margin-bottom: 0;
}

/* Enhanced Welcome Header */
.welcome-header {
    margin-bottom: 1.5rem;
    text-align: center;
    padding: 1rem 0;
}

.welcome-header h2 {
    color: var(--primary-color);
    margin-bottom: 1rem;
    font-size: 1.75rem;
    font-weight: 700;
}

.welcome-header .badge {
    margin: 0.25rem;
    font-size: 0.8rem;
}

/* Mobile-Optimized Alerts */
.alert {
    margin-bottom: 1rem;
    border-radius: 12px;
    border: none;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    padding: 1rem 1.25rem;
}

.alert-dismissible .btn-close {
    padding: 1rem;
}

/* Mobile Grid Improvements */
@media (max-width: 576px) {
    .row {
        margin-left: -0.5rem;
        margin-right: -0.5rem;
    }

    .row > * {
        padding-left: 0.5rem;
        padding-right: 0.5rem;
    }

    .welcome-header h2 {
        font-size: 1.5rem;
    }

    .welcome-header {
        margin-bottom: 1rem;
        padding: 0.5rem 0;
    }
}

@media (min-width: 768px) {
    .mb-mobile-2 { margin-bottom: 1rem; }
    .mb-mobile-3 { margin-bottom: 1.5rem; }
    .mb-mobile-4 { margin-bottom: 2rem; }
    .mb-mobile-5 { margin-bottom: 3rem; }

    .mt-mobile-2 { margin-top: 1rem; }
    .mt-mobile-3 { margin-top: 1.5rem; }
    .mt-mobile-4 { margin-top: 2rem; }
    .mt-mobile-5 { margin-top: 3rem; }

    .mobile-section {
        margin-bottom: 2rem;
    }

    .welcome-header {
        margin-bottom: 2rem;
        padding: 1.5rem 0;
    }

    .welcome-header h2 {
        font-size: 2rem;
    }
}

@media (min-width: 992px) {
    .app-content {
        padding-top: 2rem;
        padding-bottom: 2rem;
    }

    .card-body {
        padding: 1.5rem;
    }

    .mobile-section {
        margin-bottom: 2.5rem;
    }

    .welcome-header {
        margin-bottom: 2.5rem;
        padding: 2rem 0;
    }
}

/* Touch-Friendly Interactions */
@media (hover: none) and (pointer: coarse) {
    .btn:hover {
        transform: none;
    }

    .card:hover {
        transform: none;
    }

    .nav-link:hover {
        transform: none;
    }
}

/* Improved Focus States for Accessibility */
*:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

.btn:focus,
.form-control:focus,
.form-select:focus {
    outline: none;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .card {
        border: 2px solid var(--text-color);
    }

    .btn {
        border: 2px solid currentColor;
    }
}
